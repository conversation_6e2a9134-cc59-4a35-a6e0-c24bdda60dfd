import 'dart:async';

import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/utils/helpers/Interceptor/token_interceptor.dart';
import 'package:v_card/app/utils/helpers/cache/cache_options.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/helpers/injectable/injectable.config.dart';

final getIt = GetIt.instance;

@i.injectableInit
void configuration({required Widget myApp}) {
  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();
      await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      await getIt.init();

      if (kDebugMode) {
        getIt<Dio>().interceptors.add(PrettyDioLogger());
      }
      (error, stackTrace) => print('Error: $error, StackTrace: $stackTrace');

      getIt<Dio>().interceptors
        ..add(TokenInterceptor(dio: getIt<Dio>()))
        ..add(DioCacheInterceptor(options: cacheOption))
        ..add(RetryInterceptor(dio: getIt<Dio>()));

      runApp(myApp);
      await FlutterDownloader.initialize();
    },
    (error, stackTrace) => print('Error: $error, StackTrace: $stackTrace'),
    // (error, stackTrace) => FirebaseCrashlytics.instance
    //     .recordError(error, stackTrace, fatal: true),
    // zoneSpecification: ZoneSpecification(
    //   handleUncaughtError: (Zone zone, ZoneDelegate delegate, Zone parent,
    //       Object error, StackTrace stackTrace) {
    //     FirebaseCrashlytics.instance
    //         .recordError(error, stackTrace, fatal: true);
    //   },
    // ),
  );
}
